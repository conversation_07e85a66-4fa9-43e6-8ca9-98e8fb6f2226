<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeakMCP - AI-Powered Voice-to-Text Dictation Tool with MCP Integration</title>
    <meta name="description" content="Transform your voice into text with SpeakMCP - an AI-powered dictation tool featuring advanced speech recognition, intelligent post-processing, and Model Context Protocol (MCP) integration. Works with OpenAI Whisper, Groq, and Gemini.">
    <meta name="keywords" content="voice to text, dictation software, speech recognition, AI transcription, OpenAI Whisper, Groq, MCP integration, voice control, accessibility, productivity tool, electron app, cross-platform">
    <meta name="author" content="SpeakMCP Team">
    <meta name="robots" content="index, follow">
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="canonical" href="https://speakmcp.com/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://speakmcp.com/">
    <meta property="og:title" content="SpeakMCP - AI-Powered Voice-to-Text Dictation Tool">
    <meta property="og:description" content="Transform your voice into text with advanced speech recognition, intelligent post-processing, and Model Context Protocol (MCP) integration. Open source and cross-platform.">
    <meta property="og:image" content="https://speakmcp.com/og-image.png">
    <meta property="og:site_name" content="SpeakMCP">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://speakmcp.com/">
    <meta property="twitter:title" content="SpeakMCP - AI-Powered Voice-to-Text Dictation Tool">
    <meta property="twitter:description" content="Transform your voice into text with advanced speech recognition, intelligent post-processing, and Model Context Protocol (MCP) integration. Open source and cross-platform.">
    <meta property="twitter:image" content="https://speakmcp.com/og-image.png">

    <!-- Additional SEO -->
    <meta name="application-name" content="SpeakMCP">
    <meta name="theme-color" content="#000000">
    <meta name="msapplication-TileColor" content="#000000">

    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "SpeakMCP",
        "description": "AI-powered dictation tool with MCP integration - Transform your voice into text with advanced speech recognition, intelligent post-processing, and Model Context Protocol tool integration.",
        "url": "https://speakmcp.com",
        "downloadUrl": "https://github.com/aj47/SpeakMCP/releases/latest",
        "operatingSystem": ["macOS", "Windows"],
        "applicationCategory": "ProductivityApplication",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "SpeakMCP Team"
        },
        "softwareVersion": "0.0.2",
        "programmingLanguage": ["TypeScript", "JavaScript", "Rust"],
        "codeRepository": "https://github.com/aj47/SpeakMCP",
        "license": "https://github.com/aj47/SpeakMCP/blob/main/LICENSE"
    }
    </script>
    <style>
        :root {
            /* Modern liquid glass variables */
            --glass-bg: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            --glass-border: linear-gradient(135deg,
                rgba(255, 255, 255, 0.3) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.3) 100%);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(0, 0, 0, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
            --glass-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4),
                                 0 4px 12px rgba(0, 0, 0, 0.3),
                                 inset 0 1px 0 rgba(255, 255, 255, 0.3);
            --backdrop-blur: 20px;
            --border-radius: 24px;
        }

        body {
            margin: 0;
            padding: 0;
            background-color: black;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }

        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            scale: 0.5;
            overflow: visible;
            width: 100vw;
            height: 100vh;
            object-fit: cover;
            object-position: center;
            z-index: -1;
        }

        .download-container {
            position: fixed;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .download-btn {
            color: white;
            padding: 20px 40px;
            border-radius: var(--border-radius);
            font-size: 18px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            isolation: isolate;
            border: 1px solid transparent;
            cursor: pointer;
            background: var(--glass-bg);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
            box-shadow: var(--glass-shadow);
            overflow: hidden;
        }

        .download-btn::before {
            content: '';
            position: absolute;
            inset: 0;
            z-index: -1;
            border-radius: var(--border-radius);
            background: var(--glass-border);
            padding: 1px;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .download-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            pointer-events: none;
        }

        .download-btn:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--glass-hover-shadow);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.08) 50%,
                rgba(255, 255, 255, 0.15) 100%);
        }

        .download-btn:hover::after {
            left: 100%;
        }

        .download-btn:active {
            transform: translateY(-2px) scale(1.01);
            transition: all 0.1s ease;
        }

        .mac-logo, .github-logo {
            width: 20px;
            height: 20px;
            fill: currentColor;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <video class="video-background" autoplay muted loop>
        <source src="speakmcp-web.mp4" type="video/mp4">
        SpeakMCP is an AI-powered dictation tool.
    </video>

    <div class="download-container">
        <a href="https://github.com/aj47/SpeakMCP/releases/download/v0.0.2/SpeakMCP-0.0.2-arm64.dmg" class="download-btn">
            <svg class="mac-logo" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
            </svg>
            Download for Mac
        </a>
        <a href="https://github.com/aj47/SpeakMCP" class="download-btn" target="_blank" rel="noopener noreferrer">
            <svg class="github-logo" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            Open Source
        </a>
    </div>


</body>
</html>
